"""
Header components for the application
"""
import streamlit as st
from src.utils.navigation import navigate_to_home, get_current_page


def render_title_with_back_button():
    """Render the main title with back button if not on home page."""
    current_page = get_current_page()
    
    if current_page == 'home':
        st.markdown("""
        <div class="custom-title">
            <span class="title-trask">trask</span> <span class="title-ai">AIPLATFORM</span>
        </div>
        """, unsafe_allow_html=True)
    else:
        col1, col2 = st.columns([1, 100])
        with col1:
            if st.button("←", key="back_button"):
                navigate_to_home()
                st.rerun()
        with col2:
            st.markdown("""
            <div class="custom-title">
                <span class="title-trask">trask</span> <span class="title-ai">AIPLATFORM</span>
            </div>
            """, unsafe_allow_html=True)


def render_page_subtitle(title: str):
    """
    Render a page subtitle.
    
    Args:
        title (str): Subtitle text to display
    """
    st.markdown(f"""
    <div class="subtitle">
        {title}
    </div>
    """, unsafe_allow_html=True)
